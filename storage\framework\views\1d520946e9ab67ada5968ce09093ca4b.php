<?php $__env->startSection('title', 'Thống Kê Sử Dụng <PERSON>hu<PERSON>ế<PERSON>'); ?>
<?php $__env->startSection('page-title', '<PERSON><PERSON><PERSON><PERSON> l<PERSON>hu<PERSON>'); ?>
<?php $__env->startSection('breadcrumb', 'Thống kê sử dụng khuyến mãi'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .card {
        border-radius: 10px;
    }
    .table th {
        background-color: #f8f9fa;
    }
    .badge {
        font-size: 0.8em;
        padding: 0.4em 0.7em;
    }
    .stats-card {
        border-radius: 10px;
        transition: all 0.3s;
    }
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .stats-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
    .chart-container {
        height: 300px;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Thống kê tổng hợp -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="stats-card bg-primary bg-opacity-10 p-3 h-100">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-primary text-white me-3">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div>
                        <div class="small text-muted">Tổng lượt sử dụng</div>
                        <div class="h3 mb-0"><?php echo e(number_format($thongKeTongHop['tong_luot_su_dung'])); ?></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card bg-success bg-opacity-10 p-3 h-100">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-success text-white me-3">
                        <i class="fas fa-users"></i>
                    </div>
                    <div>
                        <div class="small text-muted">Số người dùng đã sử dụng</div>
                        <div class="h3 mb-0"><?php echo e(number_format($thongKeTongHop['so_nguoi_dung'])); ?></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="stats-card bg-info bg-opacity-10 p-3 h-100">
                <div class="d-flex align-items-center">
                    <div class="stats-icon bg-info text-white me-3">
                        <i class="fas fa-tag"></i>
                    </div>
                    <div>
                        <div class="small text-muted">Khuyến mãi phổ biến nhất</div>
                        <div class="h5 mb-0">
                            <?php if($thongKeTongHop['khuyenMaiPhoBien']->count() > 0): ?>
                                <?php echo e($thongKeTongHop['khuyenMaiPhoBien'][0]->ten); ?>

                            <?php else: ?>
                                Chưa có dữ liệu
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Bộ lọc và danh sách -->
        <div class="col-md-8">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 fw-bold">Lịch sử sử dụng khuyến mãi</h5>
                </div>
                <div class="card-body p-4">
                    <!-- Bộ lọc -->
                    <form method="GET" action="<?php echo e(route('admin.khuyen-mai.thong-ke-su-dung')); ?>" class="row mb-4">
                        <div class="col-md-4 mb-2">
                            <label for="khuyen_mai_id" class="form-label">Khuyến mãi</label>
                            <select name="khuyen_mai_id" id="khuyen_mai_id" class="form-select">
                                <option value="">Tất cả khuyến mãi</option>
                                <?php $__currentLoopData = $khuyenMais; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $km): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($km->id); ?>" <?php echo e(request('khuyen_mai_id') == $km->id ? 'selected' : ''); ?>>
                                        <?php echo e($km->ten); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="start_date" class="form-label">Từ ngày</label>
                            <input type="date" name="start_date" id="start_date" class="form-control"
                                value="<?php echo e(request('start_date')); ?>">
                        </div>
                        <div class="col-md-3 mb-2">
                            <label for="end_date" class="form-label">Đến ngày</label>
                            <input type="date" name="end_date" id="end_date" class="form-control"
                                value="<?php echo e(request('end_date')); ?>">
                        </div>
                        <div class="col-md-2 mb-2 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-1"></i> Lọc
                            </button>
                        </div>
                    </form>

                    <!-- Bảng dữ liệu -->
                    <div class="table-responsive">
                        <table class="table table-hover table-bordered align-middle">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col" class="text-center" style="width: 5%">#</th>
                                    <th scope="col" style="width: 30%">Khuyến mãi</th>
                                    <th scope="col" style="width: 25%">Người dùng</th>
                                    <th scope="col" style="width: 20%">Thời gian sử dụng</th>
                                    <th scope="col" class="text-center" style="width: 20%">Chi tiết</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $lichSuSuDung; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $lichSu): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td class="text-center"><?php echo e($index + $lichSuSuDung->firstItem()); ?></td>
                                    <td>
                                        <div class="fw-semibold"><?php echo e($lichSu->khuyenMai->ten ?? 'Không xác định'); ?></div>
                                        <small class="text-muted"><?php echo e($lichSu->khuyenMai->ma_khuyen_mai ?? ''); ?></small>
                                    </td>
                                    <td>
                                        <div class="fw-semibold"><?php echo e($lichSu->nguoiDung->name ?? 'Không xác định'); ?></div>
                                        <small class="text-muted"><?php echo e($lichSu->nguoiDung->email ?? ''); ?></small>
                                    </td>
                                    <td><?php echo e($lichSu->thoi_gian_su_dung instanceof \DateTime ? $lichSu->thoi_gian_su_dung->format('d/m/Y H:i') : date('d/m/Y H:i', strtotime($lichSu->thoi_gian_su_dung))); ?></td>
                                    <td class="text-center">
                                        <?php if($lichSu->khuyenMai): ?>
                                            <a href="<?php echo e(route('admin.khuyen-mai.show', $lichSu->khuyenMai->id)); ?>" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye me-1"></i> Xem
                                            </a>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Không có dữ liệu</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">Không có dữ liệu lịch sử sử dụng</div>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>

                    <!-- Phân trang -->
                    <div class="d-flex justify-content-between align-items-center mt-4">
                        <div>
                            <small class="text-muted">Hiển thị <?php echo e($lichSuSuDung->count()); ?> trong tổng số <?php echo e($lichSuSuDung->total()); ?> lịch sử</small>
                        </div>
                        <div>
                            <?php echo e($lichSuSuDung->links('pagination::bootstrap-5')); ?>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Khuyến mãi phổ biến -->
        <div class="col-md-4">
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0 fw-bold">Khuyến mãi phổ biến nhất</h5>
                </div>
                <div class="card-body p-4">
                    <?php if($thongKeTongHop['khuyenMaiPhoBien']->count() > 0): ?>
                        <div class="list-group">
                            <?php $__currentLoopData = $thongKeTongHop['khuyenMaiPhoBien']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $khuyenMai): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <a href="<?php echo e(route('admin.khuyen-mai.show', $khuyenMai->id)); ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <div class="fw-semibold"><?php echo e($khuyenMai->ten); ?></div>
                                            <small class="text-muted"><?php echo e($khuyenMai->ma_khuyen_mai); ?></small>
                                        </div>
                                        <span class="badge bg-primary rounded-pill"><?php echo e($khuyenMai->so_lan_da_su_dung); ?> lượt</span>
                                    </div>
                                </a>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <div class="text-muted">Chưa có dữ liệu khuyến mãi</div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Biểu đồ thống kê -->
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0 fw-bold">Biểu đồ thống kê</h5>
                </div>
                <div class="card-body p-4">
                    <div class="chart-container">
                        <canvas id="usageChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Dữ liệu mẫu cho biểu đồ - trong thực tế, bạn sẽ lấy dữ liệu từ controller
        const ctx = document.getElementById('usageChart').getContext('2d');
        const usageChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [
                    <?php $__currentLoopData = $thongKeTongHop['khuyenMaiPhoBien']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $khuyenMai): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        '<?php echo e(Str::limit($khuyenMai->ten, 15)); ?>',
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                ],
                datasets: [{
                    label: 'Số lần sử dụng',
                    data: [
                        <?php $__currentLoopData = $thongKeTongHop['khuyenMaiPhoBien']->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $khuyenMai): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php echo e($khuyenMai->so_lan_da_su_dung); ?>,
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(255, 99, 132, 0.6)',
                        'rgba(255, 206, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(153, 102, 255, 0.6)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/khuyen-mai/thong_ke.blade.php ENDPATH**/ ?>