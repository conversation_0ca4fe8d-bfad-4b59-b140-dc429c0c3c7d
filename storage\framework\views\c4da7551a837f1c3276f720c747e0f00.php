<?php $__env->startSection('title', 'Chỉnh Sửa <PERSON>ến <PERSON>'); ?>
<?php $__env->startSection('page-title', '<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>'); ?>
<?php $__env->startSection('breadcrumb', 'Chỉnh sửa khuyến mãi'); ?>

<?php $__env->startSection('styles'); ?>
<style>
    .card {
        border-radius: 10px;
    }
    .form-control, .form-select {
        border-radius: 8px;
    }
    .form-label {
        margin-bottom: 0.5rem;
    }
    .btn {
        border-radius: 8px;
    }
    .invalid-feedback {
        font-size: 0.9em;
    }
    .select2-container--bootstrap-5 .select2-selection {
        border-radius: 8px;
    }
</style>
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 fw-bold">Chỉnh sửa khuyến mãi</h5>
                </div>
                <div class="card-body p-4">
                    <form action="<?php echo e(route('admin.khuyen-mai.update', $khuyenMai->id)); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('PUT'); ?>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ma_khuyen_mai" class="form-label">Mã khuyến mãi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['ma_khuyen_mai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="ma_khuyen_mai" name="ma_khuyen_mai" value="<?php echo e(old('ma_khuyen_mai', $khuyenMai->ma_khuyen_mai)); ?>" required>
                                    <?php $__errorArgs = ['ma_khuyen_mai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ten" class="form-label">Tên khuyến mãi <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control <?php $__errorArgs = ['ten'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="ten" name="ten" value="<?php echo e(old('ten', $khuyenMai->ten)); ?>" required>
                                    <?php $__errorArgs = ['ten'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="mo_ta" class="form-label">Mô tả <span class="text-danger">*</span></label>
                            <textarea class="form-control <?php $__errorArgs = ['mo_ta'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                id="mo_ta" name="mo_ta" rows="3" required><?php echo e(old('mo_ta', $khuyenMai->mo_ta)); ?></textarea>
                            <?php $__errorArgs = ['mo_ta'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="loai_giam_gia" class="form-label">Loại giảm giá <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['loai_giam_gia'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="loai_giam_gia" name="loai_giam_gia" required>
                                        <option value="">-- Chọn loại giảm giá --</option>
                                        <option value="phan_tram" <?php echo e(old('loai_giam_gia', $khuyenMai->loai_giam_gia) == 'phan_tram' ? 'selected' : ''); ?>>Phần trăm (%)</option>
                                        <option value="tien" <?php echo e(old('loai_giam_gia', $khuyenMai->loai_giam_gia) == 'tien' ? 'selected' : ''); ?>>Tiền (VNĐ)</option>
                                    </select>
                                    <?php $__errorArgs = ['loai_giam_gia'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="gia_tri_giam" class="form-label">Giá trị giảm <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['gia_tri_giam'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="gia_tri_giam" name="gia_tri_giam" value="<?php echo e(old('gia_tri_giam', $khuyenMai->gia_tri_giam)); ?>" min="0" step="0.01" required>
                                    <?php $__errorArgs = ['gia_tri_giam'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="giam_toi_da" class="form-label">Giảm tối đa (VNĐ)</label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['giam_toi_da'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="giam_toi_da" name="giam_toi_da" value="<?php echo e(old('giam_toi_da', $khuyenMai->giam_toi_da)); ?>" min="0" step="0.01">
                                    <small class="text-muted">Chỉ áp dụng cho giảm giá theo phần trăm</small>
                                    <?php $__errorArgs = ['giam_toi_da'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="ap_dung_cho" class="form-label">Áp dụng cho <span class="text-danger">*</span></label>
                                    <select class="form-select <?php $__errorArgs = ['ap_dung_cho'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="ap_dung_cho" name="ap_dung_cho" required>
                                        <option value="">-- Chọn loại áp dụng --</option>
                                        <option value="ve" <?php echo e(old('ap_dung_cho', $khuyenMai->ap_dung_cho) == 've' ? 'selected' : ''); ?>>Vé xem phim</option>
                                        <option value="do_an" <?php echo e(old('ap_dung_cho', $khuyenMai->ap_dung_cho) == 'do_an' ? 'selected' : ''); ?>>Đồ ăn</option>
                                        <option value="tat_ca" <?php echo e(old('ap_dung_cho', $khuyenMai->ap_dung_cho) == 'tat_ca' ? 'selected' : ''); ?>>Tất cả</option>
                                    </select>
                                    <?php $__errorArgs = ['ap_dung_cho'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="don_toi_thieu" class="form-label">Đơn tối thiểu (VNĐ)</label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['don_toi_thieu'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="don_toi_thieu" name="don_toi_thieu" value="<?php echo e(old('don_toi_thieu', $khuyenMai->don_toi_thieu)); ?>" min="0" step="0.01">
                                    <?php $__errorArgs = ['don_toi_thieu'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="so_lan_su_dung_toi_da" class="form-label">Số lần sử dụng tối đa</label>
                                    <input type="number" class="form-control <?php $__errorArgs = ['so_lan_su_dung_toi_da'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="so_lan_su_dung_toi_da" name="so_lan_su_dung_toi_da" value="<?php echo e(old('so_lan_su_dung_toi_da', $khuyenMai->so_lan_su_dung_toi_da)); ?>" min="1">
                                    <small class="text-muted">Để trống nếu không giới hạn</small>
                                    <?php $__errorArgs = ['so_lan_su_dung_toi_da'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ngay_bat_dau" class="form-label">Ngày bắt đầu <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control <?php $__errorArgs = ['ngay_bat_dau'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="ngay_bat_dau" name="ngay_bat_dau" 
                                        value="<?php echo e(old('ngay_bat_dau', $khuyenMai->ngay_bat_dau->format('Y-m-d\TH:i'))); ?>" required>
                                    <?php $__errorArgs = ['ngay_bat_dau'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="ngay_ket_thuc" class="form-label">Ngày kết thúc <span class="text-danger">*</span></label>
                                    <input type="datetime-local" class="form-control <?php $__errorArgs = ['ngay_ket_thuc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                        id="ngay_ket_thuc" name="ngay_ket_thuc" 
                                        value="<?php echo e(old('ngay_ket_thuc', $khuyenMai->ngay_ket_thuc->format('Y-m-d\TH:i'))); ?>" required>
                                    <?php $__errorArgs = ['ngay_ket_thuc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="chi_nhanh_ids" class="form-label">Áp dụng cho chi nhánh <span class="text-danger">*</span></label>
                            <select class="form-select select2 <?php $__errorArgs = ['chi_nhanh_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                id="chi_nhanh_ids" name="chi_nhanh_ids[]" multiple required>
                                <?php $__currentLoopData = $chiNhanhs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $chiNhanh): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($chiNhanh->id); ?>" 
                                        <?php echo e(in_array($chiNhanh->id, old('chi_nhanh_ids', $selectedChiNhanhIds)) ? 'selected' : ''); ?>>
                                        <?php echo e($chiNhanh->ten_chi_nhanh); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['chi_nhanh_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3">
                            <label for="trang_thai" class="form-label">Trạng thái <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['trang_thai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                id="trang_thai" name="trang_thai" required>
                                <option value="hoat_dong" <?php echo e(old('trang_thai', $khuyenMai->trang_thai) == 'hoat_dong' ? 'selected' : ''); ?>>Hoạt động</option>
                                <option value="tam_dung" <?php echo e(old('trang_thai', $khuyenMai->trang_thai) == 'tam_dung' ? 'selected' : ''); ?>>Tạm dừng</option>
                            </select>
                            <?php $__errorArgs = ['trang_thai'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="d-flex justify-content-end mt-4">
                            <a href="<?php echo e(route('admin.khuyen-mai.index')); ?>" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-1"></i> Quay lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Cập nhật khuyến mãi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Select2 JS -->
<script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2({
            theme: 'bootstrap-5',
            placeholder: 'Chọn chi nhánh áp dụng',
            allowClear: true
        });

        // Hiển thị/ẩn trường giảm tối đa dựa trên loại giảm giá
        $('#loai_giam_gia').change(function() {
            if ($(this).val() === 'phan_tram') {
                $('#giam_toi_da').closest('.col-md-4').show();
            } else {
                $('#giam_toi_da').closest('.col-md-4').hide();
            }
        });

        // Kích hoạt khi trang tải
        $('#loai_giam_gia').trigger('change');
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\laragon\www\PolyFlix\resources\views/admin/khuyen-mai/edit.blade.php ENDPATH**/ ?>