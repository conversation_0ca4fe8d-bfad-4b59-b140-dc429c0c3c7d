<?php

namespace App\Models {

    /**
     * App\Models\Phim
     *
     * @property \Illuminate\Support\Carbon|null $update_at
     * @property \Illuminate\Support\Carbon $create_at
     * @property mixed $trang_thai
     * @property string|null $do_tuoi
     * @property string|null $quoc_gia
     * @property string|null $ngon_ngu
     * @property string|null $poster
     * @property string|null $trailer
     * @property \Illuminate\Support\Carbon|null $ngay_phat_hanh
     * @property integer|null $thoi_luong
     * @property string|null $dien_vien
     * @property string|null $dao_dien
     * @property string|null $mo_ta
     * @property string $ten_phim
     * @property int $id
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\TheLoaiPhim> $theLoais
     * @property-read int|null $theLoais_count
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTenPhim($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereMoTa($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereDaoDien($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereDienVien($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereThoiLuong($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNgayPhatHanh($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTrailer($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim wherePoster($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNgonNgu($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereQuocGia($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereDoTuoi($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTrangThai($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereCreateAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereUpdateAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim query()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim select(array|mixed $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim selectSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim selectRaw(string $expression)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim fromSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim fromRaw(string $expression, mixed $bindings)
     * @method static array createSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static array parseSub(mixed $query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery(mixed $query)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addSelect(array|mixed $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim distinct()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim from(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $table, string|null $as)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim useIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim forceIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim ignoreIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim join(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim joinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second, string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim joinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim joinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim leftJoinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim leftJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim leftJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim leftJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim rightJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim rightJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim rightJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim crossJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string|null $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim crossJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Query\JoinClause newJoinClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Query\JoinLateralClause newJoinLateralClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim mergeWheres(array $wheres, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim where(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addArrayOfWheres(array $column, string $boolean, string $method)
     * @method static array prepareValueAndOperator(string $value, string $operator, bool $useDefault)
     * @method static bool invalidOperatorAndValue(string $operator, mixed $value)
     * @method static bool invalidOperator(string $operator)
     * @method static bool isBitwiseOperator(string $operator)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhere(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second, string|null $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereRaw(string $sql, mixed $bindings, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereRaw(string $sql, mixed $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNotNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNotNull(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addDateBasedWhere(string $type, \Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNested(string $boolean)
     * @method static \Illuminate\Database\Query\Builder forNestedWhere()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addNestedWhereQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereSub(\Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, \Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addWhereExistsQuery(string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereRowValues(array $columns, string $operator, array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereRowValues(array $columns, string $operator, array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonContains(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonContains(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonDoesntContain(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonDoesntContain(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonOverlaps(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonOverlaps(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonDoesntOverlap(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonDoesntOverlap(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonContainsKey(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonContainsKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonDoesntContainKey(string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonDoesntContainKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereJsonLength(string $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereJsonLength(string $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim dynamicWhere(string $method, array $parameters)
     * @method static void addDynamic(string $segment, string $connector, array $parameters, int $index)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereFullText(string|string[] $columns, string $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereFullText(string|string[] $columns, string $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim groupBy(array|\Illuminate\Contracts\Database\Query\Expression|string ...$groups)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim groupByRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim having(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orHaving(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim havingNested(string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addNestedHavingQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim havingNull(array|string $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orHavingNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim havingNotNull(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orHavingNotNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim havingBetween(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim havingRaw(string $sql, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orHavingRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orderBy(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column, string $direction)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orderByDesc(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim latest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim oldest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim inRandomOrder(string|int $seed)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orderByRaw(string $sql, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim skip(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim offset(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim take(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim limit(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim groupLimit(int $value, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim forPage(int $page, int $perPage)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim forPageBeforeId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim forPageAfterId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim reorder(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string|null $column, string $direction)
     * @method static array removeExistingOrdersFor(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim union(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query, bool $all)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim unionAll(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim lock(string|bool $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim lockForUpdate()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim sharedLock()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim beforeQuery()
     * @method static void applyBeforeQueryCallbacks()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim afterQuery()
     * @method static mixed applyAfterQueryCallbacks(mixed $result)
     * @method static string toSql()
     * @method static string toRawSql()
     * @method static Phim|null find(int|string $id, array|string $columns)
     * @method static mixed findOr(mixed $id, callable|list<string>|string $columns, callable|null $callback)
     * @method static mixed value(string $column)
     * @method static mixed rawValue()
     * @method static mixed soleValue(string $column)
     * @method static \Illuminate\Support\Collection<int,\stdClass> get(array|string $columns)
     * @method static array runSelect()
     * @method static \Illuminate\Support\Collection withoutGroupLimitKeys(\Illuminate\Support\Collection $items)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginate(int|\Closure $perPage, array|string $columns, string $pageName, int|null $page, \Closure|int|null $total)
     * @method static \Illuminate\Contracts\Pagination\Paginator simplePaginate(int $perPage, array|string $columns, string $pageName, int|null $page)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator cursorPaginate(int|null $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static \Illuminate\Support\Collection ensureOrderForCursorPagination(bool $shouldReverse)
     * @method static int getCountForPagination(array $columns)
     * @method static array runPaginationCountQuery(array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim cloneForPaginationCount()
     * @method static array withoutSelectAliases()
     * @method static \Illuminate\Support\LazyCollection<int,\stdClass> cursor()
     * @method static void enforceOrderBy()
     * @method static mixed pluck(\Illuminate\Contracts\Database\Query\Expression|string $column, string|null $key)
     * @method static string|null stripTableForPluck(string $column)
     * @method static \Illuminate\Support\Collection pluckFromObjectColumn(array $queryResult, string $column, string $key)
     * @method static \Illuminate\Support\Collection pluckFromArrayColumn(array $queryResult, string $column, string $key)
     * @method static string implode(string $column, string $glue)
     * @method static bool exists()
     * @method static bool doesntExist()
     * @method static mixed existsOr()
     * @method static mixed doesntExistOr()
     * @method static int count(\Illuminate\Contracts\Database\Query\Expression|string $columns)
     * @method static mixed min(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed max(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed sum(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed avg(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed average(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed aggregate(string $function, array $columns)
     * @method static float|int numericAggregate(string $function, array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim setAggregate(string $function, array $columns)
     * @method static mixed onceWithColumns(array $columns, callable $callback)
     * @method static bool insert()
     * @method static int insertOrIgnore()
     * @method static int insertGetId(string|null $sequence)
     * @method static int insertUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int insertOrIgnoreUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int update()
     * @method static int updateFrom()
     * @method static bool updateOrInsert()
     * @method static int upsert(array|string $uniqueBy, array|null $update)
     * @method static int increment(string $column, float|int $amount)
     * @method static int incrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int decrement(string $column, float|int $amount)
     * @method static int decrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int delete(mixed $id)
     * @method static void truncate()
     * @method static \Illuminate\Database\Query\Builder newQuery()
     * @method static \Illuminate\Database\Query\Builder forSubQuery()
     * @method static array getColumns()
     * @method static \Illuminate\Contracts\Database\Query\Expression raw(mixed $value)
     * @method static \Illuminate\Support\Collection getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static array getBindings()
     * @method static array getRawBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim setBindings(string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim addBinding(mixed $value, string $type)
     * @method static mixed castBinding(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim mergeBindings()
     * @method static array cleanBindings()
     * @method static mixed flattenValue(mixed $value)
     * @method static string defaultKeyName()
     * @method static \Illuminate\Database\ConnectionInterface getConnection()
     * @method static \Illuminate\Database\Query\Processors\Processor getProcessor()
     * @method static \Illuminate\Database\Query\Grammars\Grammar getGrammar()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim useWritePdo()
     * @method static bool isQueryable(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim clone()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim cloneWithout()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim cloneWithoutBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim dump(mixed ...$args)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim dumpRawSql()
     * @method static void dd()
     * @method static void ddRawSql()
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim wherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim wherePastOrFuture(array|string $columns, string $operator, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereToday(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim orWhereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim whereTodayBeforeOrAfter(array|string $columns, string $operator, string $boolean)
     * @method static bool chunk(int $count, callable $callback)
     * @method static mixed chunkMap(callable $callback, int $count)
     * @method static bool each(callable $callback, int $count)
     * @method static bool chunkById(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool chunkByIdDesc(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool orderedChunkById(int $count, callable $callback, string|null $column, string|null $alias, bool $descending)
     * @method static bool eachById(callable $callback, int $count, string|null $column, string|null $alias)
     * @method static mixed lazy(int $chunkSize)
     * @method static mixed lazyById(int $chunkSize, string|null $column, string|null $alias)
     * @method static mixed lazyByIdDesc(int $chunkSize, string|null $column, string|null $alias)
     * @method static \Illuminate\Support\LazyCollection orderedLazyById(int $chunkSize, string|null $column, string|null $alias, bool $descending)
     * @method static Phim|null first(array|string $columns)
     * @method static Phim firstOrFail(array|string $columns, string|null $message)
     * @method static Phim sole(array|string $columns)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator paginateUsingCursor(int $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static string getOriginalColumnNameForCursorPagination(\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $builder, string $parameter)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginator(\Illuminate\Support\Collection $items, int $total, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\Paginator simplePaginator(\Illuminate\Support\Collection $items, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\CursorPaginator cursorPaginator(\Illuminate\Support\Collection $items, int $perPage, \Illuminate\Pagination\Cursor $cursor, array $options)
     * @method static \Illuminate\Database\Eloquent\Builder<Phim>|Phim tap(callable $callback)
     * @method static mixed pipe(callable $callback)
     * @method static mixed when(callable|\TWhenParameter|null $value, callable|null $callback, callable|null $default)
     * @method static mixed unless(callable|\TUnlessParameter|null $value, callable|null $callback, callable|null $default)
     * @method static \Illuminate\Support\Collection explain()
     * @method static mixed forwardCallTo(mixed $object, string $method, array $parameters)
     * @method static mixed forwardDecoratedCallTo(mixed $object, string $method, array $parameters)
     * @method static void throwBadMethodCallException(string $method)
     * @method static void macro(string $name, object|callable $macro)
     * @method static void mixin(object $mixin, bool $replace)
     * @method static bool hasMacro(string $name)
     * @method static void flushMacros()
     * @method static mixed macroCall(string $method, array $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class Phim extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\PhimTheLoai
     *
     * @property int $the_loai_phim_id
     * @property int $phim_id
     * @property int $id
     * @property-read \App\Models\Phim $phim
     * @property-read \App\Models\TheLoaiPhim $theLoaiPhim
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai wherePhimId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereTheLoaiPhimId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai query()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai select(array|mixed $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai selectSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai selectRaw(string $expression)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai fromSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai fromRaw(string $expression, mixed $bindings)
     * @method static array createSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static array parseSub(mixed $query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery(mixed $query)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addSelect(array|mixed $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai distinct()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai from(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $table, string|null $as)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai useIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai forceIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai ignoreIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai join(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai joinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second, string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai joinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai joinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai leftJoinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai leftJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai leftJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai leftJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai rightJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai rightJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai rightJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai crossJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string|null $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai crossJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Query\JoinClause newJoinClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Query\JoinLateralClause newJoinLateralClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai mergeWheres(array $wheres, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai where(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addArrayOfWheres(array $column, string $boolean, string $method)
     * @method static array prepareValueAndOperator(string $value, string $operator, bool $useDefault)
     * @method static bool invalidOperatorAndValue(string $operator, mixed $value)
     * @method static bool invalidOperator(string $operator)
     * @method static bool isBitwiseOperator(string $operator)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhere(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second, string|null $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereRaw(string $sql, mixed $bindings, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereRaw(string $sql, mixed $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNotNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNotNull(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addDateBasedWhere(string $type, \Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNested(string $boolean)
     * @method static \Illuminate\Database\Query\Builder forNestedWhere()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addNestedWhereQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereSub(\Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, \Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addWhereExistsQuery(string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereRowValues(array $columns, string $operator, array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereRowValues(array $columns, string $operator, array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonContains(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonContains(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonDoesntContain(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonDoesntContain(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonOverlaps(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonOverlaps(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonDoesntOverlap(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonDoesntOverlap(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonContainsKey(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonContainsKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonDoesntContainKey(string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonDoesntContainKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereJsonLength(string $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereJsonLength(string $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai dynamicWhere(string $method, array $parameters)
     * @method static void addDynamic(string $segment, string $connector, array $parameters, int $index)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereFullText(string|string[] $columns, string $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereFullText(string|string[] $columns, string $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai groupBy(array|\Illuminate\Contracts\Database\Query\Expression|string ...$groups)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai groupByRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai having(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orHaving(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai havingNested(string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addNestedHavingQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai havingNull(array|string $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orHavingNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai havingNotNull(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orHavingNotNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai havingBetween(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai havingRaw(string $sql, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orHavingRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orderBy(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column, string $direction)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orderByDesc(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai latest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai oldest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai inRandomOrder(string|int $seed)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orderByRaw(string $sql, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai skip(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai offset(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai take(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai limit(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai groupLimit(int $value, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai forPage(int $page, int $perPage)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai forPageBeforeId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai forPageAfterId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai reorder(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string|null $column, string $direction)
     * @method static array removeExistingOrdersFor(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai union(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query, bool $all)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai unionAll(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai lock(string|bool $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai lockForUpdate()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai sharedLock()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai beforeQuery()
     * @method static void applyBeforeQueryCallbacks()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai afterQuery()
     * @method static mixed applyAfterQueryCallbacks(mixed $result)
     * @method static string toSql()
     * @method static string toRawSql()
     * @method static PhimTheLoai|null find(int|string $id, array|string $columns)
     * @method static mixed findOr(mixed $id, callable|list<string>|string $columns, callable|null $callback)
     * @method static mixed value(string $column)
     * @method static mixed rawValue()
     * @method static mixed soleValue(string $column)
     * @method static \Illuminate\Support\Collection<int,\stdClass> get(array|string $columns)
     * @method static array runSelect()
     * @method static \Illuminate\Support\Collection withoutGroupLimitKeys(\Illuminate\Support\Collection $items)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginate(int|\Closure $perPage, array|string $columns, string $pageName, int|null $page, \Closure|int|null $total)
     * @method static \Illuminate\Contracts\Pagination\Paginator simplePaginate(int $perPage, array|string $columns, string $pageName, int|null $page)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator cursorPaginate(int|null $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static \Illuminate\Support\Collection ensureOrderForCursorPagination(bool $shouldReverse)
     * @method static int getCountForPagination(array $columns)
     * @method static array runPaginationCountQuery(array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai cloneForPaginationCount()
     * @method static array withoutSelectAliases()
     * @method static \Illuminate\Support\LazyCollection<int,\stdClass> cursor()
     * @method static void enforceOrderBy()
     * @method static mixed pluck(\Illuminate\Contracts\Database\Query\Expression|string $column, string|null $key)
     * @method static string|null stripTableForPluck(string $column)
     * @method static \Illuminate\Support\Collection pluckFromObjectColumn(array $queryResult, string $column, string $key)
     * @method static \Illuminate\Support\Collection pluckFromArrayColumn(array $queryResult, string $column, string $key)
     * @method static string implode(string $column, string $glue)
     * @method static bool exists()
     * @method static bool doesntExist()
     * @method static mixed existsOr()
     * @method static mixed doesntExistOr()
     * @method static int count(\Illuminate\Contracts\Database\Query\Expression|string $columns)
     * @method static mixed min(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed max(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed sum(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed avg(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed average(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed aggregate(string $function, array $columns)
     * @method static float|int numericAggregate(string $function, array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai setAggregate(string $function, array $columns)
     * @method static mixed onceWithColumns(array $columns, callable $callback)
     * @method static bool insert()
     * @method static int insertOrIgnore()
     * @method static int insertGetId(string|null $sequence)
     * @method static int insertUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int insertOrIgnoreUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int update()
     * @method static int updateFrom()
     * @method static bool updateOrInsert()
     * @method static int upsert(array|string $uniqueBy, array|null $update)
     * @method static int increment(string $column, float|int $amount)
     * @method static int incrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int decrement(string $column, float|int $amount)
     * @method static int decrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int delete(mixed $id)
     * @method static void truncate()
     * @method static \Illuminate\Database\Query\Builder newQuery()
     * @method static \Illuminate\Database\Query\Builder forSubQuery()
     * @method static array getColumns()
     * @method static \Illuminate\Contracts\Database\Query\Expression raw(mixed $value)
     * @method static \Illuminate\Support\Collection getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static array getBindings()
     * @method static array getRawBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai setBindings(string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai addBinding(mixed $value, string $type)
     * @method static mixed castBinding(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai mergeBindings()
     * @method static array cleanBindings()
     * @method static mixed flattenValue(mixed $value)
     * @method static string defaultKeyName()
     * @method static \Illuminate\Database\ConnectionInterface getConnection()
     * @method static \Illuminate\Database\Query\Processors\Processor getProcessor()
     * @method static \Illuminate\Database\Query\Grammars\Grammar getGrammar()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai useWritePdo()
     * @method static bool isQueryable(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai clone()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai cloneWithout()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai cloneWithoutBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai dump(mixed ...$args)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai dumpRawSql()
     * @method static void dd()
     * @method static void ddRawSql()
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai wherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai wherePastOrFuture(array|string $columns, string $operator, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereToday(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai orWhereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai whereTodayBeforeOrAfter(array|string $columns, string $operator, string $boolean)
     * @method static bool chunk(int $count, callable $callback)
     * @method static mixed chunkMap(callable $callback, int $count)
     * @method static bool each(callable $callback, int $count)
     * @method static bool chunkById(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool chunkByIdDesc(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool orderedChunkById(int $count, callable $callback, string|null $column, string|null $alias, bool $descending)
     * @method static bool eachById(callable $callback, int $count, string|null $column, string|null $alias)
     * @method static mixed lazy(int $chunkSize)
     * @method static mixed lazyById(int $chunkSize, string|null $column, string|null $alias)
     * @method static mixed lazyByIdDesc(int $chunkSize, string|null $column, string|null $alias)
     * @method static \Illuminate\Support\LazyCollection orderedLazyById(int $chunkSize, string|null $column, string|null $alias, bool $descending)
     * @method static PhimTheLoai|null first(array|string $columns)
     * @method static PhimTheLoai firstOrFail(array|string $columns, string|null $message)
     * @method static PhimTheLoai sole(array|string $columns)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator paginateUsingCursor(int $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static string getOriginalColumnNameForCursorPagination(\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $builder, string $parameter)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginator(\Illuminate\Support\Collection $items, int $total, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\Paginator simplePaginator(\Illuminate\Support\Collection $items, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\CursorPaginator cursorPaginator(\Illuminate\Support\Collection $items, int $perPage, \Illuminate\Pagination\Cursor $cursor, array $options)
     * @method static \Illuminate\Database\Eloquent\Builder<PhimTheLoai>|PhimTheLoai tap(callable $callback)
     * @method static mixed pipe(callable $callback)
     * @method static mixed when(callable|\TWhenParameter|null $value, callable|null $callback, callable|null $default)
     * @method static mixed unless(callable|\TUnlessParameter|null $value, callable|null $callback, callable|null $default)
     * @method static \Illuminate\Support\Collection explain()
     * @method static mixed forwardCallTo(mixed $object, string $method, array $parameters)
     * @method static mixed forwardDecoratedCallTo(mixed $object, string $method, array $parameters)
     * @method static void throwBadMethodCallException(string $method)
     * @method static void macro(string $name, object|callable $macro)
     * @method static void mixin(object $mixin, bool $replace)
     * @method static bool hasMacro(string $name)
     * @method static void flushMacros()
     * @method static mixed macroCall(string $method, array $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class PhimTheLoai extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\TheLoaiPhim
     *
     * @property \Illuminate\Support\Carbon|null $update_at
     * @property \Illuminate\Support\Carbon $create_at
     * @property mixed $trang_thai
     * @property string|null $mo_ta
     * @property string $ten_the_loai
     * @property int $id
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Phim> $phims
     * @property-read int|null $phims_count
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereTenTheLoai($value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereMoTa($value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereTrangThai($value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereCreateAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereUpdateAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim query()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim select(array|mixed $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim selectSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim selectRaw(string $expression)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim fromSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim fromRaw(string $expression, mixed $bindings)
     * @method static array createSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static array parseSub(mixed $query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery(mixed $query)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addSelect(array|mixed $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim distinct()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim from(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $table, string|null $as)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim useIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim forceIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim ignoreIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim join(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim joinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second, string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim joinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim joinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim leftJoinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim leftJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim leftJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim leftJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim rightJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim rightJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim rightJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim crossJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string|null $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim crossJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Query\JoinClause newJoinClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Query\JoinLateralClause newJoinLateralClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim mergeWheres(array $wheres, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim where(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addArrayOfWheres(array $column, string $boolean, string $method)
     * @method static array prepareValueAndOperator(string $value, string $operator, bool $useDefault)
     * @method static bool invalidOperatorAndValue(string $operator, mixed $value)
     * @method static bool invalidOperator(string $operator)
     * @method static bool isBitwiseOperator(string $operator)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhere(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second, string|null $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereRaw(string $sql, mixed $bindings, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereRaw(string $sql, mixed $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNotNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNotNull(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addDateBasedWhere(string $type, \Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNested(string $boolean)
     * @method static \Illuminate\Database\Query\Builder forNestedWhere()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addNestedWhereQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereSub(\Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, \Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addWhereExistsQuery(string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereRowValues(array $columns, string $operator, array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereRowValues(array $columns, string $operator, array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonContains(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonContains(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonDoesntContain(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonDoesntContain(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonOverlaps(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonOverlaps(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonDoesntOverlap(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonDoesntOverlap(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonContainsKey(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonContainsKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonDoesntContainKey(string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonDoesntContainKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereJsonLength(string $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereJsonLength(string $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim dynamicWhere(string $method, array $parameters)
     * @method static void addDynamic(string $segment, string $connector, array $parameters, int $index)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereFullText(string|string[] $columns, string $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereFullText(string|string[] $columns, string $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim groupBy(array|\Illuminate\Contracts\Database\Query\Expression|string ...$groups)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim groupByRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim having(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orHaving(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim havingNested(string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addNestedHavingQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim havingNull(array|string $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orHavingNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim havingNotNull(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orHavingNotNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim havingBetween(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim havingRaw(string $sql, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orHavingRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orderBy(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column, string $direction)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orderByDesc(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim latest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim oldest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim inRandomOrder(string|int $seed)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orderByRaw(string $sql, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim skip(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim offset(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim take(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim limit(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim groupLimit(int $value, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim forPage(int $page, int $perPage)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim forPageBeforeId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim forPageAfterId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim reorder(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string|null $column, string $direction)
     * @method static array removeExistingOrdersFor(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim union(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query, bool $all)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim unionAll(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim lock(string|bool $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim lockForUpdate()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim sharedLock()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim beforeQuery()
     * @method static void applyBeforeQueryCallbacks()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim afterQuery()
     * @method static mixed applyAfterQueryCallbacks(mixed $result)
     * @method static string toSql()
     * @method static string toRawSql()
     * @method static TheLoaiPhim|null find(int|string $id, array|string $columns)
     * @method static mixed findOr(mixed $id, callable|list<string>|string $columns, callable|null $callback)
     * @method static mixed value(string $column)
     * @method static mixed rawValue()
     * @method static mixed soleValue(string $column)
     * @method static \Illuminate\Support\Collection<int,\stdClass> get(array|string $columns)
     * @method static array runSelect()
     * @method static \Illuminate\Support\Collection withoutGroupLimitKeys(\Illuminate\Support\Collection $items)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginate(int|\Closure $perPage, array|string $columns, string $pageName, int|null $page, \Closure|int|null $total)
     * @method static \Illuminate\Contracts\Pagination\Paginator simplePaginate(int $perPage, array|string $columns, string $pageName, int|null $page)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator cursorPaginate(int|null $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static \Illuminate\Support\Collection ensureOrderForCursorPagination(bool $shouldReverse)
     * @method static int getCountForPagination(array $columns)
     * @method static array runPaginationCountQuery(array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim cloneForPaginationCount()
     * @method static array withoutSelectAliases()
     * @method static \Illuminate\Support\LazyCollection<int,\stdClass> cursor()
     * @method static void enforceOrderBy()
     * @method static mixed pluck(\Illuminate\Contracts\Database\Query\Expression|string $column, string|null $key)
     * @method static string|null stripTableForPluck(string $column)
     * @method static \Illuminate\Support\Collection pluckFromObjectColumn(array $queryResult, string $column, string $key)
     * @method static \Illuminate\Support\Collection pluckFromArrayColumn(array $queryResult, string $column, string $key)
     * @method static string implode(string $column, string $glue)
     * @method static bool exists()
     * @method static bool doesntExist()
     * @method static mixed existsOr()
     * @method static mixed doesntExistOr()
     * @method static int count(\Illuminate\Contracts\Database\Query\Expression|string $columns)
     * @method static mixed min(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed max(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed sum(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed avg(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed average(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed aggregate(string $function, array $columns)
     * @method static float|int numericAggregate(string $function, array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim setAggregate(string $function, array $columns)
     * @method static mixed onceWithColumns(array $columns, callable $callback)
     * @method static bool insert()
     * @method static int insertOrIgnore()
     * @method static int insertGetId(string|null $sequence)
     * @method static int insertUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int insertOrIgnoreUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int update()
     * @method static int updateFrom()
     * @method static bool updateOrInsert()
     * @method static int upsert(array|string $uniqueBy, array|null $update)
     * @method static int increment(string $column, float|int $amount)
     * @method static int incrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int decrement(string $column, float|int $amount)
     * @method static int decrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int delete(mixed $id)
     * @method static void truncate()
     * @method static \Illuminate\Database\Query\Builder newQuery()
     * @method static \Illuminate\Database\Query\Builder forSubQuery()
     * @method static array getColumns()
     * @method static \Illuminate\Contracts\Database\Query\Expression raw(mixed $value)
     * @method static \Illuminate\Support\Collection getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static array getBindings()
     * @method static array getRawBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim setBindings(string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim addBinding(mixed $value, string $type)
     * @method static mixed castBinding(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim mergeBindings()
     * @method static array cleanBindings()
     * @method static mixed flattenValue(mixed $value)
     * @method static string defaultKeyName()
     * @method static \Illuminate\Database\ConnectionInterface getConnection()
     * @method static \Illuminate\Database\Query\Processors\Processor getProcessor()
     * @method static \Illuminate\Database\Query\Grammars\Grammar getGrammar()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim useWritePdo()
     * @method static bool isQueryable(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim clone()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim cloneWithout()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim cloneWithoutBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim dump(mixed ...$args)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim dumpRawSql()
     * @method static void dd()
     * @method static void ddRawSql()
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim wherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim wherePastOrFuture(array|string $columns, string $operator, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereToday(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim orWhereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim whereTodayBeforeOrAfter(array|string $columns, string $operator, string $boolean)
     * @method static bool chunk(int $count, callable $callback)
     * @method static mixed chunkMap(callable $callback, int $count)
     * @method static bool each(callable $callback, int $count)
     * @method static bool chunkById(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool chunkByIdDesc(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool orderedChunkById(int $count, callable $callback, string|null $column, string|null $alias, bool $descending)
     * @method static bool eachById(callable $callback, int $count, string|null $column, string|null $alias)
     * @method static mixed lazy(int $chunkSize)
     * @method static mixed lazyById(int $chunkSize, string|null $column, string|null $alias)
     * @method static mixed lazyByIdDesc(int $chunkSize, string|null $column, string|null $alias)
     * @method static \Illuminate\Support\LazyCollection orderedLazyById(int $chunkSize, string|null $column, string|null $alias, bool $descending)
     * @method static TheLoaiPhim|null first(array|string $columns)
     * @method static TheLoaiPhim firstOrFail(array|string $columns, string|null $message)
     * @method static TheLoaiPhim sole(array|string $columns)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator paginateUsingCursor(int $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static string getOriginalColumnNameForCursorPagination(\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $builder, string $parameter)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginator(\Illuminate\Support\Collection $items, int $total, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\Paginator simplePaginator(\Illuminate\Support\Collection $items, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\CursorPaginator cursorPaginator(\Illuminate\Support\Collection $items, int $perPage, \Illuminate\Pagination\Cursor $cursor, array $options)
     * @method static \Illuminate\Database\Eloquent\Builder<TheLoaiPhim>|TheLoaiPhim tap(callable $callback)
     * @method static mixed pipe(callable $callback)
     * @method static mixed when(callable|\TWhenParameter|null $value, callable|null $callback, callable|null $default)
     * @method static mixed unless(callable|\TUnlessParameter|null $value, callable|null $callback, callable|null $default)
     * @method static \Illuminate\Support\Collection explain()
     * @method static mixed forwardCallTo(mixed $object, string $method, array $parameters)
     * @method static mixed forwardDecoratedCallTo(mixed $object, string $method, array $parameters)
     * @method static void throwBadMethodCallException(string $method)
     * @method static void macro(string $name, object|callable $macro)
     * @method static void mixin(object $mixin, bool $replace)
     * @method static bool hasMacro(string $name)
     * @method static void flushMacros()
     * @method static mixed macroCall(string $method, array $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class TheLoaiPhim extends \Illuminate\Database\Eloquent\Model
    {
        //
    }

    /**
     * App\Models\User
     *
     * @property \Illuminate\Support\Carbon|null $updated_at
     * @property \Illuminate\Support\Carbon|null $created_at
     * @property string|null $remember_token
     * @property string $password
     * @property \Illuminate\Support\Carbon|null $email_verified_at
     * @property string $email
     * @property string $name
     * @property int $id
     * @property-read \Illuminate\Database\Eloquent\Collection<int, \Illuminate\Notifications\DatabaseNotification> $notifications
     * @property-read int|null $notifications_count
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereId($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereName($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereEmail($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereEmailVerifiedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User wherePassword($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereRememberToken($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereCreatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereUpdatedAt($value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User newModelQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User newQuery()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User query()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User select(array|mixed $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User selectSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User selectRaw(string $expression)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User fromSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User fromRaw(string $expression, mixed $bindings)
     * @method static array createSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static array parseSub(mixed $query)
     * @method static mixed prependDatabaseNameIfCrossDatabaseQuery(mixed $query)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addSelect(array|mixed $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User distinct()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User from(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $table, string|null $as)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User useIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User forceIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User ignoreIndex(string $index)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User join(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User joinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second, string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User joinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second, string $type, bool $where)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User joinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User leftJoinLateral(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User leftJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User leftJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User leftJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User rightJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User rightJoinWhere(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string $operator, \Illuminate\Contracts\Database\Query\Expression|string $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User rightJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as, \Closure|\Illuminate\Contracts\Database\Query\Expression|string $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User crossJoin(\Illuminate\Contracts\Database\Query\Expression|string $table, \Closure|\Illuminate\Contracts\Database\Query\Expression|string|null $first, string|null $operator, \Illuminate\Contracts\Database\Query\Expression|string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User crossJoinSub(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query, string $as)
     * @method static \Illuminate\Database\Query\JoinClause newJoinClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Query\JoinLateralClause newJoinLateralClause(string $type, \Illuminate\Contracts\Database\Query\Expression|string $table)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User mergeWheres(array $wheres, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User where(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addArrayOfWheres(array $column, string $boolean, string $method)
     * @method static array prepareValueAndOperator(string $value, string $operator, bool $useDefault)
     * @method static bool invalidOperatorAndValue(string $operator, mixed $value)
     * @method static bool invalidOperator(string $operator)
     * @method static bool isBitwiseOperator(string $operator)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhere(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNot(\Closure|string|array|\Illuminate\Contracts\Database\Query\Expression $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second, string|null $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereColumn(\Illuminate\Contracts\Database\Query\Expression|string|array $first, string|null $operator, string|null $second)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereRaw(string $sql, mixed $bindings, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereRaw(string $sql, mixed $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNotLike(\Illuminate\Contracts\Database\Query\Expression|string $column, string $value, bool $caseSensitive)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNotIn(\Illuminate\Contracts\Database\Query\Expression|string $column, mixed $values)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereIntegerInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereIntegerNotInRaw(string $column, \Illuminate\Contracts\Support\Arrayable|array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNull(string|array|\Illuminate\Contracts\Database\Query\Expression $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNotNull(string|array|\Illuminate\Contracts\Database\Query\Expression $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNotBetween(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNotBetweenColumns(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNotNull(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereDate(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereTime(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|null $operator, \DateTimeInterface|string|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereDay(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereMonth(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereYear(\Illuminate\Contracts\Database\Query\Expression|string $column, \DateTimeInterface|string|int|null $operator, \DateTimeInterface|string|int|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addDateBasedWhere(string $type, \Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNested(string $boolean)
     * @method static \Illuminate\Database\Query\Builder forNestedWhere()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addNestedWhereQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereSub(\Illuminate\Contracts\Database\Query\Expression|string $column, string $operator, \Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNotExists(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $callback)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addWhereExistsQuery(string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereRowValues(array $columns, string $operator, array $values, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereRowValues(array $columns, string $operator, array $values)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonContains(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonContains(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonDoesntContain(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonDoesntContain(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonOverlaps(string $column, mixed $value, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonOverlaps(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonDoesntOverlap(string $column, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonDoesntOverlap(string $column, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonContainsKey(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonContainsKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonDoesntContainKey(string $column, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonDoesntContainKey(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereJsonLength(string $column, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereJsonLength(string $column, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User dynamicWhere(string $method, array $parameters)
     * @method static void addDynamic(string $segment, string $connector, array $parameters, int $index)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereFullText(string|string[] $columns, string $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereFullText(string|string[] $columns, string $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereAll(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereAny(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNone(\Illuminate\Contracts\Database\Query\Expression[]|\Closure[]|string[] $columns, mixed $operator, mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User groupBy(array|\Illuminate\Contracts\Database\Query\Expression|string ...$groups)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User groupByRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User having(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orHaving(\Illuminate\Contracts\Database\Query\Expression|\Closure|string $column, \DateTimeInterface|string|int|float|null $operator, \Illuminate\Contracts\Database\Query\Expression|\DateTimeInterface|string|int|float|null $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User havingNested(string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addNestedHavingQuery(\Illuminate\Database\Query\Builder $query, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User havingNull(array|string $columns, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orHavingNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User havingNotNull(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orHavingNotNull(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User havingBetween(string $column, string $boolean, bool $not)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User havingRaw(string $sql, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orHavingRaw(string $sql)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orderBy(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column, string $direction)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orderByDesc(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User latest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User oldest(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User inRandomOrder(string|int $seed)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orderByRaw(string $sql, array $bindings)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User skip(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User offset(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User take(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User limit(int $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User groupLimit(int $value, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User forPage(int $page, int $perPage)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User forPageBeforeId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User forPageAfterId(int $perPage, int|null $lastId, string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User reorder(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Contracts\Database\Query\Expression|string|null $column, string $direction)
     * @method static array removeExistingOrdersFor(string $column)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User union(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query, bool $all)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User unionAll(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $query)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User lock(string|bool $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User lockForUpdate()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User sharedLock()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User beforeQuery()
     * @method static void applyBeforeQueryCallbacks()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User afterQuery()
     * @method static mixed applyAfterQueryCallbacks(mixed $result)
     * @method static string toSql()
     * @method static string toRawSql()
     * @method static User|null find(int|string $id, array|string $columns)
     * @method static mixed findOr(mixed $id, callable|list<string>|string $columns, callable|null $callback)
     * @method static mixed value(string $column)
     * @method static mixed rawValue()
     * @method static mixed soleValue(string $column)
     * @method static \Illuminate\Support\Collection<int,\stdClass> get(array|string $columns)
     * @method static array runSelect()
     * @method static \Illuminate\Support\Collection withoutGroupLimitKeys(\Illuminate\Support\Collection $items)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginate(int|\Closure $perPage, array|string $columns, string $pageName, int|null $page, \Closure|int|null $total)
     * @method static \Illuminate\Contracts\Pagination\Paginator simplePaginate(int $perPage, array|string $columns, string $pageName, int|null $page)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator cursorPaginate(int|null $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static \Illuminate\Support\Collection ensureOrderForCursorPagination(bool $shouldReverse)
     * @method static int getCountForPagination(array $columns)
     * @method static array runPaginationCountQuery(array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User cloneForPaginationCount()
     * @method static array withoutSelectAliases()
     * @method static \Illuminate\Support\LazyCollection<int,\stdClass> cursor()
     * @method static void enforceOrderBy()
     * @method static mixed pluck(\Illuminate\Contracts\Database\Query\Expression|string $column, string|null $key)
     * @method static string|null stripTableForPluck(string $column)
     * @method static \Illuminate\Support\Collection pluckFromObjectColumn(array $queryResult, string $column, string $key)
     * @method static \Illuminate\Support\Collection pluckFromArrayColumn(array $queryResult, string $column, string $key)
     * @method static string implode(string $column, string $glue)
     * @method static bool exists()
     * @method static bool doesntExist()
     * @method static mixed existsOr()
     * @method static mixed doesntExistOr()
     * @method static int count(\Illuminate\Contracts\Database\Query\Expression|string $columns)
     * @method static mixed min(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed max(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed sum(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed avg(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed average(\Illuminate\Contracts\Database\Query\Expression|string $column)
     * @method static mixed aggregate(string $function, array $columns)
     * @method static float|int numericAggregate(string $function, array $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User setAggregate(string $function, array $columns)
     * @method static mixed onceWithColumns(array $columns, callable $callback)
     * @method static bool insert()
     * @method static int insertOrIgnore()
     * @method static int insertGetId(string|null $sequence)
     * @method static int insertUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int insertOrIgnoreUsing(\Closure|\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed>|string $query)
     * @method static int update()
     * @method static int updateFrom()
     * @method static bool updateOrInsert()
     * @method static int upsert(array|string $uniqueBy, array|null $update)
     * @method static int increment(string $column, float|int $amount)
     * @method static int incrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int decrement(string $column, float|int $amount)
     * @method static int decrementEach(array<string,float|int|numeric-string> $columns, array<string,mixed> $extra)
     * @method static int delete(mixed $id)
     * @method static void truncate()
     * @method static \Illuminate\Database\Query\Builder newQuery()
     * @method static \Illuminate\Database\Query\Builder forSubQuery()
     * @method static array getColumns()
     * @method static \Illuminate\Contracts\Database\Query\Expression raw(mixed $value)
     * @method static \Illuminate\Support\Collection getUnionBuilders()
     * @method static mixed getLimit()
     * @method static mixed getOffset()
     * @method static array getBindings()
     * @method static array getRawBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User setBindings(string $type)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User addBinding(mixed $value, string $type)
     * @method static mixed castBinding(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User mergeBindings()
     * @method static array cleanBindings()
     * @method static mixed flattenValue(mixed $value)
     * @method static string defaultKeyName()
     * @method static \Illuminate\Database\ConnectionInterface getConnection()
     * @method static \Illuminate\Database\Query\Processors\Processor getProcessor()
     * @method static \Illuminate\Database\Query\Grammars\Grammar getGrammar()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User useWritePdo()
     * @method static bool isQueryable(mixed $value)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User clone()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User cloneWithout()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User cloneWithoutBindings()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User dump(mixed ...$args)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User dumpRawSql()
     * @method static void dd()
     * @method static void ddRawSql()
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User wherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWherePast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNowOrPast(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereNowOrFuture(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User wherePastOrFuture(array|string $columns, string $operator, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereToday(array|string $columns, string $boolean)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereBeforeToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereTodayOrBefore(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereAfterToday(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User orWhereTodayOrAfter(array|string $columns)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User whereTodayBeforeOrAfter(array|string $columns, string $operator, string $boolean)
     * @method static bool chunk(int $count, callable $callback)
     * @method static mixed chunkMap(callable $callback, int $count)
     * @method static bool each(callable $callback, int $count)
     * @method static bool chunkById(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool chunkByIdDesc(int $count, callable $callback, string|null $column, string|null $alias)
     * @method static bool orderedChunkById(int $count, callable $callback, string|null $column, string|null $alias, bool $descending)
     * @method static bool eachById(callable $callback, int $count, string|null $column, string|null $alias)
     * @method static mixed lazy(int $chunkSize)
     * @method static mixed lazyById(int $chunkSize, string|null $column, string|null $alias)
     * @method static mixed lazyByIdDesc(int $chunkSize, string|null $column, string|null $alias)
     * @method static \Illuminate\Support\LazyCollection orderedLazyById(int $chunkSize, string|null $column, string|null $alias, bool $descending)
     * @method static User|null first(array|string $columns)
     * @method static User firstOrFail(array|string $columns, string|null $message)
     * @method static User sole(array|string $columns)
     * @method static \Illuminate\Contracts\Pagination\CursorPaginator paginateUsingCursor(int $perPage, array|string $columns, string $cursorName, \Illuminate\Pagination\Cursor|string|null $cursor)
     * @method static string getOriginalColumnNameForCursorPagination(\Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder<mixed> $builder, string $parameter)
     * @method static \Illuminate\Pagination\LengthAwarePaginator paginator(\Illuminate\Support\Collection $items, int $total, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\Paginator simplePaginator(\Illuminate\Support\Collection $items, int $perPage, int $currentPage, array $options)
     * @method static \Illuminate\Pagination\CursorPaginator cursorPaginator(\Illuminate\Support\Collection $items, int $perPage, \Illuminate\Pagination\Cursor $cursor, array $options)
     * @method static \Illuminate\Database\Eloquent\Builder<User>|User tap(callable $callback)
     * @method static mixed pipe(callable $callback)
     * @method static mixed when(callable|\TWhenParameter|null $value, callable|null $callback, callable|null $default)
     * @method static mixed unless(callable|\TUnlessParameter|null $value, callable|null $callback, callable|null $default)
     * @method static \Illuminate\Support\Collection explain()
     * @method static mixed forwardCallTo(mixed $object, string $method, array $parameters)
     * @method static mixed forwardDecoratedCallTo(mixed $object, string $method, array $parameters)
     * @method static void throwBadMethodCallException(string $method)
     * @method static void macro(string $name, object|callable $macro)
     * @method static void mixin(object $mixin, bool $replace)
     * @method static bool hasMacro(string $name)
     * @method static void flushMacros()
     * @method static mixed macroCall(string $method, array $parameters)
     * @mixin \Illuminate\Database\Query\Builder
     */
    class User extends \Illuminate\Foundation\Auth\User
    {
        //
    }

}